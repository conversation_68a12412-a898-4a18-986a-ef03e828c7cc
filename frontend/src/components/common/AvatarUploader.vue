<template>
  <div class="avatar-uploader">
    <!-- 头像显示区域 -->
    <div class="avatar-display" @click="triggerFileSelect">
      <div class="avatar-container">
        <img
          v-if="currentAvatar"
          :src="currentAvatar"
          :alt="altText"
          class="avatar-image"
        />
        <div v-else class="avatar-placeholder">
          <el-icon :size="40">
            <User />
          </el-icon>
          <span class="placeholder-text">{{ placeholderText }}</span>
        </div>

        <!-- 悬停遮罩 -->
        <div class="avatar-overlay">
          <el-icon :size="24">
            <Camera />
          </el-icon>
          <span class="overlay-text">{{ overlayText }}</span>
        </div>
      </div>
    </div>

    <!-- 隐藏的文件输入 -->
    <input
      ref="fileInputRef"
      type="file"
      accept="image/*"
      style="display: none"
      @change="handleFileSelect"
    />

    <!-- 裁剪对话框 -->
    <el-dialog
      v-model="cropDialogVisible"
      :title="dialogTitle"
      width="1000px"
      :close-on-click-modal="false"
      :close-on-press-escape="false"
      class="avatar-crop-dialog"
      @close="handleDialogClose"
    >
      <div class="crop-container">
        <!-- 裁剪区域 -->
        <div class="crop-area">
          <div class="crop-wrapper">
            <canvas
              ref="cropCanvasRef"
              class="crop-canvas"
              @mousedown="startCrop"
              @mousemove="updateCrop"
              @mouseup="endCrop"
              @touchstart="startCrop"
              @touchmove="updateCrop"
              @touchend="endCrop"
            ></canvas>

            <!-- 裁剪框调整控制点 -->
            <div
              v-if="cropState.cropWidth > 0"
              class="crop-resize-handles"
              :style="{
                left: cropState.cropX + 'px',
                top: cropState.cropY + 'px',
                width: cropState.cropWidth + 'px',
                height: cropState.cropHeight + 'px',
              }"
            >
              <!-- 四个角的调整点 -->
              <div
                class="resize-handle corner-tl"
                @mousedown="startResize('tl', $event)"
              ></div>
              <div
                class="resize-handle corner-tr"
                @mousedown="startResize('tr', $event)"
              ></div>
              <div
                class="resize-handle corner-bl"
                @mousedown="startResize('bl', $event)"
              ></div>
              <div
                class="resize-handle corner-br"
                @mousedown="startResize('br', $event)"
              ></div>

              <!-- 四边的调整点 -->
              <div
                class="resize-handle edge-t"
                @mousedown="startResize('t', $event)"
              ></div>
              <div
                class="resize-handle edge-r"
                @mousedown="startResize('r', $event)"
              ></div>
              <div
                class="resize-handle edge-b"
                @mousedown="startResize('b', $event)"
              ></div>
              <div
                class="resize-handle edge-l"
                @mousedown="startResize('l', $event)"
              ></div>
            </div>
          </div>
        </div>

        <!-- 预览区域 -->
        <div class="preview-area">
          <div class="preview-header">
            <el-icon><View /></el-icon>
            <span>预览效果</span>
          </div>

          <div class="preview-content">
            <!-- 圆形预览 -->
            <div class="preview-item">
              <div class="preview-label">头像预览</div>
              <div ref="previewCircleRef" class="preview-circle"></div>
              <div class="preview-size">{{ avatarSize }}x{{ avatarSize }}px</div>
            </div>

            <!-- 方形预览 -->
            <div class="preview-item">
              <div class="preview-label">方形预览</div>
              <div ref="previewSquareRef" class="preview-square"></div>
              <div class="preview-size">{{ avatarSize }}x{{ avatarSize }}px</div>
            </div>
          </div>
        </div>
      </div>

      <!-- 操作按钮 -->
      <template #footer>
        <div class="dialog-footer">
          <div class="crop-controls">
            <el-button-group>
              <el-button :icon="RefreshLeft" @click="rotateLeft">
                左转
              </el-button>
              <el-button :icon="RefreshRight" @click="rotateRight">
                右转
              </el-button>
              <el-button :icon="Switch" @click="flipHorizontal">
                水平翻转
              </el-button>
              <el-button :icon="SwitchButton" @click="flipVertical">
                垂直翻转
              </el-button>
              <el-button :icon="Refresh" @click="resetCrop"> 重置 </el-button>
            </el-button-group>
          </div>

          <div class="action-buttons">
            <el-button @click="handleCancel"> 取消 </el-button>
            <el-button :icon="FolderOpened" @click="reSelectFile">
              重新选择
            </el-button>
            <el-button
              type="primary"
              :loading="processing"
              :icon="Check"
              @click="handleConfirm"
            >
              {{ processing ? "处理中..." : "确认裁剪" }}
            </el-button>
          </div>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, nextTick, onMounted, onBeforeUnmount } from "vue";
import { ElMessage } from "element-plus";
import {
  User,
  Camera,
  View,
  RefreshLeft,
  RefreshRight,
  Switch,
  SwitchButton,
  Refresh,
  FolderOpened,
  Check,
} from "@element-plus/icons-vue";

// Props定义
const props = defineProps({
  // 当前头像URL
  modelValue: {
    type: String,
    default: "",
  },
  // 头像尺寸
  avatarSize: {
    type: Number,
    default: 100,
  },
  // 裁剪输出尺寸
  outputSize: {
    type: Number,
    default: 200,
  },
  // 输出质量 (0-1)
  outputQuality: {
    type: Number,
    default: 0.9,
  },
  // 输出格式
  outputFormat: {
    type: String,
    default: "image/jpeg",
  },
  // 最大文件大小 (字节)
  maxFileSize: {
    type: Number,
    default: 5 * 1024 * 1024, // 5MB
  },
  // 占位符文本
  placeholderText: {
    type: String,
    default: "点击上传头像",
  },
  // 悬停文本
  overlayText: {
    type: String,
    default: "更换头像",
  },
  // 对话框标题
  dialogTitle: {
    type: String,
    default: "裁剪头像",
  },
  // alt文本
  altText: {
    type: String,
    default: "用户头像",
  },
  // 是否禁用
  disabled: {
    type: Boolean,
    default: false,
  },
});

// Emits定义
const emit = defineEmits([
  "update:modelValue",
  "crop-complete",
  "crop-cancel",
  "file-select",
]);

// 响应式数据
const fileInputRef = ref();
const cropCanvasRef = ref();
const previewCircleRef = ref();
const previewSquareRef = ref();

const cropDialogVisible = ref(false);
const originalImageUrl = ref("");
const currentAvatar = ref(props.modelValue);
const processing = ref(false);

// 裁剪相关状态
const cropState = ref({
  isDragging: false,
  isResizing: false,
  resizeType: "",
  startX: 0,
  startY: 0,
  cropX: 0,
  cropY: 0,
  cropWidth: 200,
  cropHeight: 200,
  scale: 1,
  rotation: 0,
});

let originalImage = null;
let canvasContext = null;

// 文件选择处理
const triggerFileSelect = () => {
  if (props.disabled) {
    return;
  }

  if (fileInputRef.value) {
    fileInputRef.value.click();
  } else {
    console.error("❌ File input ref is null!");
  }
};

const handleFileSelect = (event) => {
  const file = event.target.files[0];
  if (!file) return;

  // 验证文件类型
  if (!file.type.startsWith("image/")) {
    ElMessage.error("请选择图片文件");
    return;
  }

  // 验证文件大小
  if (file.size > props.maxFileSize) {
    const maxSizeMB = (props.maxFileSize / (1024 * 1024)).toFixed(1);
    ElMessage.error(`文件大小不能超过 ${maxSizeMB}MB`);
    return;
  }

  // 读取文件并显示裁剪对话框
  const reader = new FileReader();
  reader.onload = (e) => {
    originalImageUrl.value = e.target.result;
    cropDialogVisible.value = true;

    nextTick(() => {
      initCropper();
    });
  };
  reader.onerror = (e) => {
    console.error("文件读取失败:", e);
    ElMessage.error("文件读取失败");
  };
  reader.readAsDataURL(file);

  // 触发文件选择事件
  emit("file-select", file);

  // 清空input值，允许重复选择同一文件
  event.target.value = "";
};

// 计算Canvas最佳尺寸
const calculateCanvasSize = () => {
  const cropWrapper = cropCanvasRef.value?.parentElement;
  if (!cropWrapper) return { width: 450, height: 350 };

  const wrapperRect = cropWrapper.getBoundingClientRect();
  const padding = 60; // 增加内边距

  // 限制最大尺寸，确保预览区域有足够空间
  const maxWidth = Math.min(450, wrapperRect.width - padding);
  const maxHeight = Math.min(350, wrapperRect.height - padding);

  // 保持4:3的宽高比
  const aspectRatio = 4 / 3;

  let canvasWidth, canvasHeight;

  if (maxWidth / maxHeight > aspectRatio) {
    // 容器更宽，以高度为准
    canvasHeight = maxHeight;
    canvasWidth = canvasHeight * aspectRatio;
  } else {
    // 容器更高，以宽度为准
    canvasWidth = maxWidth;
    canvasHeight = canvasWidth / aspectRatio;
  }

  return {
    width: Math.floor(canvasWidth),
    height: Math.floor(canvasHeight)
  };
};

// 初始化Canvas裁剪器
const initCropper = () => {
  if (!cropCanvasRef.value) return;

  const canvas = cropCanvasRef.value;
  canvasContext = canvas.getContext("2d");

  // 创建图片对象
  originalImage = new Image();
  originalImage.onload = () => {
    // 动态计算canvas尺寸
    const { width: canvasWidth, height: canvasHeight } = calculateCanvasSize();

    canvas.width = canvasWidth;
    canvas.height = canvasHeight;

    // 计算图片在canvas中的显示尺寸
    const imgAspect = originalImage.width / originalImage.height;
    const canvasAspect = canvasWidth / canvasHeight;

    let displayWidth, displayHeight;
    if (imgAspect > canvasAspect) {
      // 图片更宽，以高度为准
      displayHeight = canvasHeight * 0.9;
      displayWidth = displayHeight * imgAspect;
    } else {
      // 图片更高，以宽度为准
      displayWidth = canvasWidth * 0.9;
      displayHeight = displayWidth / imgAspect;
    }

    // 初始化裁剪区域（居中的正方形）
    const cropSize = Math.min(displayWidth, displayHeight) * 0.7;
    cropState.value.cropX = (canvasWidth - cropSize) / 2;
    cropState.value.cropY = (canvasHeight - cropSize) / 2;
    cropState.value.cropWidth = cropSize;
    cropState.value.cropHeight = cropSize;
    cropState.value.scale = 1;
    cropState.value.rotation = 0;

    // 绘制图片和裁剪框
    drawCanvas();
    updatePreview();
  };
  originalImage.src = originalImageUrl.value;
};

// 绘制Canvas
const drawCanvas = () => {
  if (!canvasContext || !originalImage) return;

  const canvas = cropCanvasRef.value;
  const { width, height } = canvas;

  // 清空画布
  canvasContext.clearRect(0, 0, width, height);

  // 计算图片显示尺寸和位置
  const imgAspect = originalImage.width / originalImage.height;
  const canvasAspect = width / height;

  let displayWidth, displayHeight, offsetX, offsetY;
  if (imgAspect > canvasAspect) {
    displayHeight = height * 0.9;
    displayWidth = displayHeight * imgAspect;
    offsetX = (width - displayWidth) / 2;
    offsetY = (height - displayHeight) / 2;
  } else {
    displayWidth = width * 0.9;
    displayHeight = displayWidth / imgAspect;
    offsetX = (width - displayWidth) / 2;
    offsetY = (height - displayHeight) / 2;
  }

  // 绘制原图
  canvasContext.save();
  canvasContext.translate(width / 2, height / 2);
  canvasContext.rotate((cropState.value.rotation * Math.PI) / 180);
  canvasContext.scale(cropState.value.scale, cropState.value.scale);
  canvasContext.drawImage(
    originalImage,
    -displayWidth / 2,
    -displayHeight / 2,
    displayWidth,
    displayHeight
  );
  canvasContext.restore();

  // 绘制遮罩
  canvasContext.fillStyle = "rgba(0, 0, 0, 0.5)";
  canvasContext.fillRect(0, 0, width, height);

  // 清除裁剪区域的遮罩
  canvasContext.globalCompositeOperation = "destination-out";
  canvasContext.fillRect(
    cropState.value.cropX,
    cropState.value.cropY,
    cropState.value.cropWidth,
    cropState.value.cropHeight,
  );
  canvasContext.globalCompositeOperation = "source-over";

  // 注意：裁剪框边框和控制点由HTML元素 .crop-resize-handles 负责显示
  // 这里不再绘制重复的边框，避免覆盖图片内容
};

// 更新预览
const updatePreview = () => {
  if (!canvasContext || !originalImage) return;

  const cropCanvas = document.createElement("canvas");
  const cropCtx = cropCanvas.getContext("2d");

  cropCanvas.width = props.avatarSize;
  cropCanvas.height = props.avatarSize;

  // 计算裁剪区域在原图中的位置
  const scaleX =
    originalImage.width / (cropCanvasRef.value.width / cropState.value.scale);
  const scaleY =
    originalImage.height / (cropCanvasRef.value.height / cropState.value.scale);

  const sourceX = (cropState.value.cropX / cropState.value.scale) * scaleX;
  const sourceY = (cropState.value.cropY / cropState.value.scale) * scaleY;
  const sourceWidth =
    (cropState.value.cropWidth / cropState.value.scale) * scaleX;
  const sourceHeight =
    (cropState.value.cropHeight / cropState.value.scale) * scaleY;

  // 绘制裁剪后的图片
  cropCtx.drawImage(
    originalImage,
    sourceX,
    sourceY,
    sourceWidth,
    sourceHeight,
    0,
    0,
    props.avatarSize,
    props.avatarSize,
  );

  // 更新圆形预览
  if (previewCircleRef.value) {
    previewCircleRef.value.innerHTML = "";
    const circleCanvas = cropCanvas.cloneNode();
    const circleCtx = circleCanvas.getContext("2d");
    circleCtx.drawImage(cropCanvas, 0, 0);
    circleCanvas.style.width = "100%";
    circleCanvas.style.height = "100%";
    circleCanvas.style.borderRadius = "50%";
    previewCircleRef.value.appendChild(circleCanvas);
  }

  // 更新方形预览
  if (previewSquareRef.value) {
    previewSquareRef.value.innerHTML = "";
    const squareCanvas = cropCanvas.cloneNode();
    const squareCtx = squareCanvas.getContext("2d");
    squareCtx.drawImage(cropCanvas, 0, 0);
    squareCanvas.style.width = "100%";
    squareCanvas.style.height = "100%";
    previewSquareRef.value.appendChild(squareCanvas);
  }
};

// 鼠标/触摸交互
const getEventPos = (event) => {
  const rect = cropCanvasRef.value.getBoundingClientRect();
  const clientX =
    event.clientX || (event.touches && event.touches[0]?.clientX) || 0;
  const clientY =
    event.clientY || (event.touches && event.touches[0]?.clientY) || 0;

  return {
    x: clientX - rect.left,
    y: clientY - rect.top,
  };
};

const startCrop = (event) => {
  event.preventDefault();
  const pos = getEventPos(event);

  cropState.value.isDragging = true;
  cropState.value.startX = pos.x;
  cropState.value.startY = pos.y;
};

const updateCrop = (event) => {
  if (!cropState.value.isDragging || cropState.value.isResizing) return;

  event.preventDefault();
  const pos = getEventPos(event);
  const deltaX = pos.x - cropState.value.startX;
  const deltaY = pos.y - cropState.value.startY;

  // 移动裁剪框
  const newX = cropState.value.cropX + deltaX;
  const newY = cropState.value.cropY + deltaY;

  // 边界检查
  const canvas = cropCanvasRef.value;
  if (newX >= 0 && newX + cropState.value.cropWidth <= canvas.width) {
    cropState.value.cropX = newX;
  }
  if (newY >= 0 && newY + cropState.value.cropHeight <= canvas.height) {
    cropState.value.cropY = newY;
  }

  cropState.value.startX = pos.x;
  cropState.value.startY = pos.y;

  drawCanvas();
  updatePreview();
};

const endCrop = (event) => {
  event.preventDefault();
  cropState.value.isDragging = false;
  cropState.value.isResizing = false;
};

// 调整裁剪框大小
const startResize = (type, event) => {
  event.preventDefault();
  event.stopPropagation();

  cropState.value.isResizing = true;
  cropState.value.resizeType = type;

  const pos = getEventPos(event);
  cropState.value.startX = pos.x;
  cropState.value.startY = pos.y;

  // 添加全局事件监听
  document.addEventListener("mousemove", handleResize);
  document.addEventListener("mouseup", endResize);
};

const handleResize = (event) => {
  if (!cropState.value.isResizing) return;

  event.preventDefault();
  const pos = getEventPos(event);
  const deltaX = pos.x - cropState.value.startX;
  const deltaY = pos.y - cropState.value.startY;

  const canvas = cropCanvasRef.value;
  const minSize = 50; // 最小裁剪框大小

  let newX = cropState.value.cropX;
  let newY = cropState.value.cropY;
  let newWidth = cropState.value.cropWidth;
  let newHeight = cropState.value.cropHeight;

  // 根据调整类型处理
  switch (cropState.value.resizeType) {
    case "tl": // 左上角
      newX += deltaX;
      newY += deltaY;
      newWidth -= deltaX;
      newHeight -= deltaY;
      break;
    case "tr": // 右上角
      newY += deltaY;
      newWidth += deltaX;
      newHeight -= deltaY;
      break;
    case "bl": // 左下角
      newX += deltaX;
      newWidth -= deltaX;
      newHeight += deltaY;
      break;
    case "br": // 右下角
      newWidth += deltaX;
      newHeight += deltaY;
      break;
    case "t": // 上边
      newY += deltaY;
      newHeight -= deltaY;
      break;
    case "r": // 右边
      newWidth += deltaX;
      break;
    case "b": // 下边
      newHeight += deltaY;
      break;
    case "l": // 左边
      newX += deltaX;
      newWidth -= deltaX;
      break;
  }

  // 保持正方形（可选）
  if (event.shiftKey) {
    const size = Math.min(newWidth, newHeight);
    newWidth = size;
    newHeight = size;
  }

  // 边界检查
  if (
    newWidth >= minSize &&
    newHeight >= minSize &&
    newX >= 0 &&
    newY >= 0 &&
    newX + newWidth <= canvas.width &&
    newY + newHeight <= canvas.height
  ) {
    cropState.value.cropX = newX;
    cropState.value.cropY = newY;
    cropState.value.cropWidth = newWidth;
    cropState.value.cropHeight = newHeight;

    cropState.value.startX = pos.x;
    cropState.value.startY = pos.y;

    drawCanvas();
    updatePreview();
  }
};

const endResize = (event) => {
  event.preventDefault();
  cropState.value.isResizing = false;
  cropState.value.resizeType = "";

  // 移除全局事件监听
  document.removeEventListener("mousemove", handleResize);
  document.removeEventListener("mouseup", endResize);
};

// 裁剪操作
const rotateLeft = () => {
  cropState.value.rotation -= 90;
  drawCanvas();
  updatePreview();
};

const rotateRight = () => {
  cropState.value.rotation += 90;
  drawCanvas();
  updatePreview();
};

const flipHorizontal = () => {
  // 简化实现：重置到初始状态
  resetCrop();
};

const flipVertical = () => {
  // 简化实现：重置到初始状态
  resetCrop();
};

const resetCrop = () => {
  if (!cropCanvasRef.value || !originalImage) return;

  const canvas = cropCanvasRef.value;
  const cropSize = Math.min(canvas.width, canvas.height) * 0.8;

  cropState.value.cropX = (canvas.width - cropSize) / 2;
  cropState.value.cropY = (canvas.height - cropSize) / 2;
  cropState.value.cropWidth = cropSize;
  cropState.value.cropHeight = cropSize;
  cropState.value.rotation = 0;

  drawCanvas();
  updatePreview();
};

// 重新选择文件
const reSelectFile = () => {
  triggerFileSelect();
};

// 确认裁剪
const handleConfirm = async () => {
  if (!canvasContext || !originalImage) return;

  try {
    processing.value = true;

    // 创建输出canvas
    const outputCanvas = document.createElement("canvas");
    const outputCtx = outputCanvas.getContext("2d");

    outputCanvas.width = props.outputSize;
    outputCanvas.height = props.outputSize;

    // 计算裁剪区域在原图中的位置
    const scaleX =
      originalImage.width / (cropCanvasRef.value.width / cropState.value.scale);
    const scaleY =
      originalImage.height /
      (cropCanvasRef.value.height / cropState.value.scale);

    const sourceX = (cropState.value.cropX / cropState.value.scale) * scaleX;
    const sourceY = (cropState.value.cropY / cropState.value.scale) * scaleY;
    const sourceWidth =
      (cropState.value.cropWidth / cropState.value.scale) * scaleX;
    const sourceHeight =
      (cropState.value.cropHeight / cropState.value.scale) * scaleY;

    // 绘制裁剪后的图片
    outputCtx.drawImage(
      originalImage,
      sourceX,
      sourceY,
      sourceWidth,
      sourceHeight,
      0,
      0,
      props.outputSize,
      props.outputSize,
    );

    // 转换为Blob
    const blob = await new Promise((resolve, reject) => {
      outputCanvas.toBlob(
        (blob) => {
          if (blob) {
            resolve(blob);
          } else {
            reject(new Error("图片处理失败"));
          }
        },
        props.outputFormat,
        props.outputQuality,
      );
    });

    // 生成预览URL
    const previewUrl = URL.createObjectURL(blob);
    currentAvatar.value = previewUrl;

    // 触发事件
    emit("update:modelValue", previewUrl);
    emit("crop-complete", {
      blob,
      canvas: outputCanvas,
      previewUrl,
      file: blob,
      size: blob.size,
    });

    cropDialogVisible.value = false;
    ElMessage.success("头像裁剪完成");
  } catch (error) {
    console.error("裁剪失败:", error);
    ElMessage.error("裁剪失败: " + error.message);
  } finally {
    processing.value = false;
  }
};

// 取消裁剪
const handleCancel = () => {
  cropDialogVisible.value = false;
  emit("crop-cancel");
};

// 对话框关闭处理
const handleDialogClose = () => {
  // 清理canvas和图片资源
  if (canvasContext) {
    canvasContext.clearRect(
      0,
      0,
      cropCanvasRef.value.width,
      cropCanvasRef.value.height,
    );
    canvasContext = null;
  }

  originalImage = null;

  if (originalImageUrl.value) {
    URL.revokeObjectURL(originalImageUrl.value);
    originalImageUrl.value = "";
  }

  // 重置裁剪状态
  cropState.value = {
    isDragging: false,
    isResizing: false,
    resizeType: "",
    startX: 0,
    startY: 0,
    cropX: 0,
    cropY: 0,
    cropWidth: 200,
    cropHeight: 200,
    scale: 1,
    rotation: 0,
  };

  // 清理事件监听器
  document.removeEventListener("mousemove", handleResize);
  document.removeEventListener("mouseup", endResize);
};

// 组件卸载时清理
onBeforeUnmount(() => {
  if (originalImageUrl.value) {
    URL.revokeObjectURL(originalImageUrl.value);
  }

  if (currentAvatar.value && currentAvatar.value.startsWith("blob:")) {
    URL.revokeObjectURL(currentAvatar.value);
  }
});

// 监听modelValue变化
import { watch } from "vue";
watch(
  () => props.modelValue,
  (newValue) => {
    currentAvatar.value = newValue;
  },
);
</script>

<style scoped>
.avatar-uploader {
  display: inline-block;
}

.avatar-display {
  cursor: pointer;
  transition: all 0.3s ease;
  position: relative;
}

.avatar-display:hover {
  transform: scale(1.02) translateY(-1px);
}

.avatar-container {
  position: relative;
  width: v-bind('avatarSize + "px"');
  height: v-bind('avatarSize + "px"');
  border-radius: 50%;
  overflow: hidden;
  border: 2px solid #ffffff;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
  transition: all 0.3s ease;
  background: linear-gradient(135deg, #f8f9fa 0%, #ffffff 100%);
}

.avatar-container:hover {
  border-color: #409eff;
  box-shadow: 0 4px 16px rgba(64, 158, 255, 0.15);
  transform: none;
}

.avatar-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
  display: block;
}

.avatar-placeholder {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
  color: #6c757d;
  font-size: 13px;
  font-weight: 500;
}

.placeholder-text {
  margin-top: 10px;
  text-align: center;
  line-height: 1.3;
  font-weight: 400;
  opacity: 0.8;
}

.avatar-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(
    135deg,
    rgba(64, 158, 255, 0.9) 0%,
    rgba(103, 194, 58, 0.9) 100%
  );
  color: white;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  opacity: 0;
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  font-size: 13px;
  font-weight: 500;
  pointer-events: none;
  backdrop-filter: blur(2px);
}

.avatar-container:hover .avatar-overlay {
  opacity: 1;
  transform: scale(1.02);
}

.overlay-text {
  margin-top: 6px;
  text-align: center;
  font-weight: 400;
  letter-spacing: 0.5px;
}

/* 裁剪对话框样式 */
.avatar-crop-dialog {
  --el-dialog-padding-primary: 20px;
}

.avatar-crop-dialog :deep(.el-dialog) {
  margin: 3vh auto;
  border-radius: 12px;
  box-shadow: 0 12px 32px rgba(0, 0, 0, 0.1);
  max-width: 95vw;
  max-height: 90vh;
}

.avatar-crop-dialog :deep(.el-dialog__header) {
  padding: 20px 24px 16px;
  border-bottom: 1px solid #f0f2f5;
}

.avatar-crop-dialog :deep(.el-dialog__body) {
  padding: 20px;
  height: 600px;
  overflow: hidden;
}

.crop-container {
  display: grid;
  grid-template-columns: 3fr 2fr;
  gap: 24px;
  height: 100%;
  min-height: 560px;
}

.crop-area {
  display: flex;
  flex-direction: column;
  min-width: 0; /* 允许网格项收缩 */
}

.crop-wrapper {
  width: 100%;
  height: 100%;
  position: relative;
  background: #f8fafc;
  border-radius: 12px;
  overflow: visible; /* 修改：允许裁剪框边框显示 */
  border: 2px solid #e2e8f0;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: inset 0 2px 4px rgba(0, 0, 0, 0.06);
  padding: 20px;
}

.crop-canvas {
  display: block;
  cursor: move;
  border-radius: 8px;
  border: 1px solid #cbd5e1;
  background: #fff;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}

/* 裁剪框调整控制点 */
.crop-resize-handles {
  position: absolute;
  pointer-events: none;
  border: 2px solid #409eff;
  border-radius: 4px;
  box-shadow: 0 0 0 1px rgba(255, 255, 255, 0.8);
  z-index: 10; /* 增加：确保裁剪框在最上层 */
}

.resize-handle {
  position: absolute;
  background: #409eff;
  border: 2px solid #ffffff;
  border-radius: 50%;
  pointer-events: auto;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
  transition: all 0.2s ease;
}

.resize-handle:hover {
  background: #337ecc;
  transform: scale(1.2);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.25);
}

/* 角落控制点 */
.corner-tl,
.corner-tr,
.corner-bl,
.corner-br {
  width: 12px;
  height: 12px;
}

.corner-tl {
  top: -6px;
  left: -6px;
  cursor: nw-resize;
}

.corner-tr {
  top: -6px;
  right: -6px;
  cursor: ne-resize;
}

.corner-bl {
  bottom: -6px;
  left: -6px;
  cursor: sw-resize;
}

.corner-br {
  bottom: -6px;
  right: -6px;
  cursor: se-resize;
}

/* 边缘控制点 */
.edge-t,
.edge-r,
.edge-b,
.edge-l {
  width: 10px;
  height: 10px;
}

.edge-t {
  top: -5px;
  left: 50%;
  transform: translateX(-50%);
  cursor: n-resize;
}

.edge-r {
  right: -5px;
  top: 50%;
  transform: translateY(-50%);
  cursor: e-resize;
}

.edge-b {
  bottom: -5px;
  left: 50%;
  transform: translateX(-50%);
  cursor: s-resize;
}

.edge-l {
  left: -5px;
  top: 50%;
  transform: translateY(-50%);
  cursor: w-resize;
}

.preview-area {
  display: flex;
  flex-direction: column;
  min-width: 0; /* 允许网格项收缩 */
  background: #f8fafc;
  border-radius: 12px;
  border: 2px solid #e2e8f0;
  overflow: hidden;
}

.preview-header {
  display: flex;
  align-items: center;
  gap: 8px;
  font-weight: 600;
  color: #1e293b;
  padding: 16px 20px;
  background: #fff;
  border-bottom: 1px solid #e2e8f0;
  font-size: 14px;
}

.preview-content {
  flex: 1;
  padding: 20px;
  display: flex;
  flex-direction: column;
  gap: 24px;
  justify-content: center;
}

.preview-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 12px;
  padding: 16px;
  background: #fff;
  border-radius: 8px;
  border: 1px solid #e2e8f0;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.preview-label {
  font-size: 13px;
  font-weight: 500;
  color: #475569;
  text-align: center;
}

.preview-circle,
.preview-square {
  width: v-bind('avatarSize + "px"');
  height: v-bind('avatarSize + "px"');
  border: 2px solid #e2e8f0;
  background: #ffffff;
  display: flex;
  align-items: center;
  justify-content: center;
  overflow: hidden;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  transition: all 0.2s ease;
}

.preview-circle:hover,
.preview-square:hover {
  border-color: #3b82f6;
  box-shadow: 0 4px 8px rgba(59, 130, 246, 0.15);
  transform: translateY(-1px);
}

.preview-circle {
  border-radius: 50%;
}

.preview-square {
  border-radius: 8px;
}

.preview-size {
  font-size: 12px;
  color: #64748b;
  text-align: center;
  margin-top: 4px;
  font-weight: 400;
}

.dialog-footer {
  display: flex;
  justify-content: space-between;
  align-items: center;
  flex-wrap: wrap;
  gap: 16px;
  margin-top: 20px;
  padding-top: 20px;
  border-top: 1px solid #e4e7ed;
}

.crop-controls {
  flex: 1;
  display: flex;
  justify-content: flex-start;
}

.crop-controls .el-button-group {
  flex-wrap: wrap;
  gap: 4px;
}

.action-buttons {
  display: flex;
  gap: 8px;
  flex-shrink: 0;
}

/* 响应式设计 */
@media (max-width: 1200px) {
  .avatar-crop-dialog :deep(.el-dialog) {
    width: 95vw !important;
    margin: 2vh auto;
  }

  .crop-container {
    grid-template-columns: 2fr 1fr;
    gap: 20px;
    height: 520px;
  }

  .avatar-crop-dialog :deep(.el-dialog__body) {
    height: 520px;
  }
}

@media (max-width: 1024px) {
  .avatar-crop-dialog :deep(.el-dialog) {
    width: 95vw !important;
    margin: 2vh auto;
  }

  .crop-container {
    grid-template-columns: 3fr 2fr;
    gap: 16px;
    height: 480px;
  }

  .avatar-crop-dialog :deep(.el-dialog__body) {
    height: 480px;
  }

  .preview-content {
    padding: 16px;
    gap: 20px;
  }
}

@media (max-width: 768px) {
  .avatar-crop-dialog :deep(.el-dialog) {
    width: 95vw !important;
    margin: 1vh auto;
  }

  .crop-container {
    grid-template-columns: 1fr;
    grid-template-rows: 1fr auto;
    height: auto;
    gap: 16px;
  }

  .avatar-crop-dialog :deep(.el-dialog__body) {
    height: auto;
    max-height: 85vh;
    overflow-y: auto;
  }

  .crop-area {
    height: 300px;
    min-height: 300px;
  }

  .crop-wrapper {
    border-radius: 8px;
    min-height: 280px;
  }

  .preview-area {
    order: 2;
  }

  .preview-content {
    flex-direction: row;
    justify-content: center;
    gap: 16px;
    padding: 16px;
  }

  .preview-item {
    flex: 1;
    padding: 12px;
  }

  .dialog-footer {
    flex-direction: column;
    align-items: stretch;
    gap: 16px;
    margin: 20px -20px -20px -20px;
    padding: 16px;
  }

  .crop-controls {
    order: 2;
    justify-content: center;
  }

  .crop-controls .el-button-group {
    justify-content: center;
    flex-wrap: wrap;
    gap: 4px;
  }

  .crop-controls .el-button {
    font-size: 12px;
    padding: 6px 12px;
    margin: 2px;
  }

  .action-buttons {
    order: 1;
    justify-content: center;
    flex-wrap: wrap;
    gap: 8px;
  }

  .action-buttons .el-button {
    flex: 1;
    min-width: 100px;
    font-size: 14px;
  }
}

@media (max-width: 480px) {
  .avatar-crop-dialog :deep(.el-dialog__body) {
    padding: 12px;
  }

  .crop-container {
    gap: 12px;
  }

  .crop-area {
    height: 250px;
    min-height: 250px;
  }

  .preview-content {
    flex-direction: column;
    align-items: center;
    padding: 12px;
    gap: 12px;
  }

  .preview-item {
    padding: 8px;
    width: 100%;
    max-width: 200px;
  }

  .crop-controls .el-button {
    font-size: 12px;
    padding: 8px 12px;
  }

  .action-buttons .el-button {
    font-size: 14px;
    padding: 10px 16px;
  }
}

/* 加载状态 */
.avatar-container.loading {
  pointer-events: none;
  opacity: 0.6;
}

.avatar-container.loading::after {
  content: "";
  position: absolute;
  top: 50%;
  left: 50%;
  width: 20px;
  height: 20px;
  margin: -10px 0 0 -10px;
  border: 2px solid #409eff;
  border-radius: 50%;
  border-top-color: transparent;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  to {
    transform: rotate(360deg);
  }
}

/* 禁用状态 */
.avatar-uploader.disabled .avatar-display {
  cursor: not-allowed;
  opacity: 0.6;
}

.avatar-uploader.disabled .avatar-container:hover {
  transform: none;
  border-color: #e4e7ed;
  box-shadow: none;
}

.avatar-uploader.disabled .avatar-overlay {
  display: none;
}
</style>
